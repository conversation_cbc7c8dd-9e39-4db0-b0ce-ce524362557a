<?php
/**
 * Admin Dashboard Homepage
 * CYPTSHOP - Task 7.1.2: Dashboard Overview
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Require admin access
requireAdmin();

// No database connection needed for JSON-based system

$pageTitle = 'Admin Dashboard';
$bodyClass = 'admin-dashboard';

// Get dashboard statistics from JSON files
try {
    $users = getJsonData(USERS_JSON) ?: [];
    $orders = getJsonData(ORDERS_JSON) ?: [];
    $products = getJsonData(PRODUCTS_JSON) ?: [];
    $contacts = getJsonData(CONTACTS_JSON) ?: [];

    $totalCustomers = count(array_filter($users, function($user) { return ($user['role'] ?? '') === 'customer'; }));
    $totalOrders = count($orders);
    $totalProducts = count($products);
    $totalRevenue = array_sum(array_column($orders, 'total'));
    $totalContacts = count($contacts);

    // Recent activity
    $recentOrders = array_slice(array_reverse($orders), 0, 5);
    $recentContacts = array_slice(array_reverse($contacts), 0, 5);
} catch (Exception $e) {
    // Fallback values if database is not available
    $totalCustomers = 0;
    $totalOrders = 0;
    $totalProducts = 0;
    $totalRevenue = 0;
    $totalContacts = 0;
    $recentOrders = [];
    $recentContacts = [];
    error_log('Admin dashboard error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - CYPTSHOP Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js for Dashboard Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">

    <style>
        body {
            background-color: var(--admin-bg, #1a1a1a);
            color: var(--admin-text, #ffffff);
        }

        /* Enhanced Dashboard Styles */
        .dashboard-card {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 12px;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.15);
            border-color: rgba(0, 255, 255, 0.4);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00FFFF, #FF00FF);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dashboard-card:hover::before {
            opacity: 1;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
            background: linear-gradient(45deg, #00FFFF, #FFFFFF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: rgba(255, 255, 255, 0.7);
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .chart-container {
            position: relative;
            height: 300px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 20px;
        }

        .activity-card {
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(64, 64, 64, 0.5);
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .activity-card:hover {
            border-color: rgba(0, 255, 255, 0.4);
            background: rgba(0, 255, 255, 0.05);
        }

        .quick-action-btn {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
            border: 1px solid rgba(0, 255, 255, 0.3);
            color: #ffffff;
            border-radius: 8px;
            padding: 12px 20px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            margin-bottom: 8px;
        }

        .quick-action-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2));
            border-color: rgba(0, 255, 255, 0.6);
            color: #00FFFF;
            transform: translateX(4px);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(26, 26, 26, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-top: 3px solid #00FFFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Dashboard Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        /* Avatar Styling */
        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }

        /* Enhanced Table Styling */
        .table-hover tbody tr:hover {
            background-color: rgba(0, 255, 255, 0.05) !important;
        }

        /* Button Group Enhancements */
        .btn-group-sm .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* Progress Bar Enhancements */
        .progress {
            background-color: rgba(64, 64, 64, 0.3);
        }

        /* Dashboard Header Enhancements - Fixed Overlap Issue */
        .dashboard-header {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05));
            border-radius: 12px;
            border: 1px solid rgba(0, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        /* Ensure dashboard buttons don't overlap with sidebar toggle */
        .dashboard-header .btn-group {
            position: relative;
            z-index: 2;
        }

        /* Add proper spacing for toggle button area */
        @media (min-width: 768px) {
            .main-content {
                padding-left: 20px; /* Add left padding to avoid toggle button */
            }
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .stat-value {
                font-size: 2rem;
            }

            .dashboard-card {
                margin-bottom: 1rem;
            }

            .chart-container {
                height: 250px;
            }
        }

        /* Custom Scrollbar for Tables */
        .table-responsive::-webkit-scrollbar {
            height: 6px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: rgba(26, 26, 26, 0.5);
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 255, 0.3);
            border-radius: 3px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 255, 0.5);
        }
    </style>
</head>
<body class="<?php echo $bodyClass; ?>"><?php
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <!-- Enhanced Dashboard Header -->
        <div class="dashboard-header py-4 mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-2">
                        <div class="stat-icon me-3">
                            <i class="fas fa-tachometer-alt text-cyan"></i>
                        </div>
                        <div>
                            <h1 class="h2 text-white mb-1">Dashboard Overview</h1>
                            <p class="text-off-white mb-0">
                                <i class="fas fa-clock me-1"></i>
                                Last updated: <span id="lastUpdated"><?php echo date('M j, Y g:i A'); ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success me-2">
                            <i class="fas fa-circle me-1"></i>System Online
                        </span>
                        <span class="text-off-white small">
                            <i class="fas fa-users me-1"></i>
                            Welcome back, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="btn-group mb-2">
                        <button type="button" class="btn btn-cyan" onclick="refreshDashboard()" id="refreshBtn">
                            <i class="fas fa-sync me-1"></i>Refresh Data
                        </button>
                        <button type="button" class="btn btn-outline-cyan dropdown-toggle dropdown-toggle-split"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="visually-hidden">Toggle Dropdown</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-dark">
                            <li><a class="dropdown-item" href="#" onclick="exportDashboardData()">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="printDashboard()">
                                <i class="fas fa-print me-2"></i>Print Report
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/analytics.php">
                                <i class="fas fa-chart-line me-2"></i>Advanced Analytics
                            </a></li>
                        </ul>
                    </div>
                    <div class="d-block">
                        <small class="text-off-white">
                            <i class="fas fa-server me-1"></i>
                            Server Status: <span class="text-success">Healthy</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>


        <!-- Enhanced Statistics Cards -->
        <div class="row mb-4" id="statsContainer">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-4">
                        <div class="loading-overlay" id="customersLoading">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="stat-icon">
                                <i class="fas fa-users text-cyan"></i>
                            </div>
                            <div class="text-end">
                                <div class="stat-change text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+12%
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="stat-value" id="totalCustomersValue"><?php echo $totalCustomers; ?></div>
                            <div class="stat-label">Total Customers</div>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-cyan" role="progressbar" style="width: 75%"></div>
                        </div>
                        <small class="text-off-white mt-2 d-block">
                            <i class="fas fa-clock me-1"></i>Updated just now
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-4">
                        <div class="loading-overlay" id="ordersLoading">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart text-magenta"></i>
                            </div>
                            <div class="text-end">
                                <div class="stat-change text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+8%
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="stat-value" id="totalOrdersValue"><?php echo $totalOrders; ?></div>
                            <div class="stat-label">Total Orders</div>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-magenta" role="progressbar" style="width: 60%"></div>
                        </div>
                        <small class="text-off-white mt-2 d-block">
                            <i class="fas fa-clock me-1"></i>Updated just now
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-4">
                        <div class="loading-overlay" id="productsLoading">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="stat-icon">
                                <i class="fas fa-box text-yellow"></i>
                            </div>
                            <div class="text-end">
                                <div class="stat-change text-warning">
                                    <i class="fas fa-minus me-1"></i>0%
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="stat-value" id="totalProductsValue"><?php echo $totalProducts; ?></div>
                            <div class="stat-label">Total Products</div>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-yellow" role="progressbar" style="width: 45%"></div>
                        </div>
                        <small class="text-off-white mt-2 d-block">
                            <i class="fas fa-clock me-1"></i>Updated just now
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-4">
                        <div class="loading-overlay" id="revenueLoading">
                            <div class="loading-spinner"></div>
                        </div>
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign text-cyan"></i>
                            </div>
                            <div class="text-end">
                                <div class="stat-change text-success">
                                    <i class="fas fa-arrow-up me-1"></i>+25%
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="stat-value" id="totalRevenueValue">$<?php echo number_format($totalRevenue, 2); ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-cyan" role="progressbar" style="width: 85%"></div>
                        </div>
                        <small class="text-off-white mt-2 d-block">
                            <i class="fas fa-clock me-1"></i>Updated just now
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Analytics & Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="text-white mb-0">
                                <i class="fas fa-chart-line me-2 text-cyan"></i>
                                Sales Analytics
                            </h5>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-cyan active" onclick="updateChart('7days')">7 Days</button>
                                <button class="btn btn-outline-cyan" onclick="updateChart('30days')">30 Days</button>
                                <button class="btn btn-outline-cyan" onclick="updateChart('90days')">90 Days</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="salesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="dashboard-card h-100">
                    <div class="card-body p-4">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-bolt me-2 text-magenta"></i>
                            Quick Actions
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="<?php echo SITE_URL; ?>/admin/products.php?action=add" class="quick-action-btn">
                                <i class="fas fa-plus me-2"></i>Add New Product
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/coupons.php" class="quick-action-btn">
                                <i class="fas fa-ticket-alt me-2"></i>Manage Coupons
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="quick-action-btn">
                                <i class="fas fa-shopping-cart me-2"></i>View All Orders
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/portfolio.php" class="quick-action-btn">
                                <i class="fas fa-images me-2"></i>Manage Portfolio
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/hero.php" class="quick-action-btn">
                                <i class="fas fa-image me-2"></i>Update Hero Content
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/analytics.php" class="quick-action-btn">
                                <i class="fas fa-chart-bar me-2"></i>Advanced Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary Statistics Row -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="dashboard-card">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-ticket-alt text-magenta"></i>
                            </div>
                            <div>
                                <div class="h6 text-white mb-0">0</div>
                                <small class="text-off-white">Active Coupons</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="dashboard-card">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-images text-cyan"></i>
                            </div>
                            <div>
                                <div class="h6 text-white mb-0">0</div>
                                <small class="text-off-white">Portfolio Items</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="dashboard-card">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-tags text-success"></i>
                            </div>
                            <div>
                                <div class="h6 text-white mb-0"><?php echo count(getJsonData(CATEGORIES_JSON)); ?></div>
                                <small class="text-off-white">Categories</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="dashboard-card">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon me-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-percentage text-yellow"></i>
                            </div>
                            <div>
                                <div class="h6 text-white mb-0">$0.00</div>
                                <small class="text-off-white">Coupon Savings</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Activity Section -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="dashboard-card">
                    <div class="card-body p-0">
                        <div class="d-flex justify-content-between align-items-center p-4 border-bottom border-dark">
                            <h5 class="text-white mb-0">
                                <i class="fas fa-clock me-2 text-cyan"></i>
                                Recent Orders
                            </h5>
                            <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="btn btn-sm btn-outline-cyan">
                                View All <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="border-0 text-cyan">Order ID</th>
                                        <th class="border-0 text-cyan">Customer</th>
                                        <th class="border-0 text-cyan">Total</th>
                                        <th class="border-0 text-cyan">Status</th>
                                        <th class="border-0 text-cyan">Date</th>
                                        <th class="border-0 text-cyan">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($orders)): ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-5">
                                                <div class="text-gray-400">
                                                    <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                                    <p class="mb-0">No orders yet</p>
                                                    <small>Orders will appear here once customers start purchasing</small>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach (array_slice($orders, -5) as $order): ?>
                                            <tr class="activity-card border-0">
                                                <td class="border-0">
                                                    <span class="text-cyan fw-semibold">#<?php echo htmlspecialchars($order['id']); ?></span>
                                                </td>
                                                <td class="border-0">
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-dark-grey-2 rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <i class="fas fa-user text-cyan"></i>
                                                        </div>
                                                        <span class="text-white"><?php echo htmlspecialchars($order['customer_name'] ?? 'Guest'); ?></span>
                                                    </div>
                                                </td>
                                                <td class="border-0">
                                                    <span class="text-white fw-semibold">$<?php echo number_format($order['total'], 2); ?></span>
                                                </td>
                                                <td class="border-0">
                                                    <span class="badge bg-<?php echo $order['status'] === 'completed' ? 'success' : ($order['status'] === 'pending' ? 'warning' : 'secondary'); ?> px-3 py-2">
                                                        <i class="fas fa-<?php echo $order['status'] === 'completed' ? 'check' : ($order['status'] === 'pending' ? 'clock' : 'times'); ?> me-1"></i>
                                                        <?php echo ucfirst($order['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="border-0">
                                                    <span class="text-off-white"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></span>
                                                    <br><small class="text-gray-400"><?php echo date('g:i A', strtotime($order['created_at'])); ?></small>
                                                </td>
                                                <td class="border-0">
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-cyan btn-sm" title="View Order">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success btn-sm" title="Process Order">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 mb-4">
                <div class="dashboard-card h-100">
                    <div class="card-body p-4">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-chart-pie me-2 text-magenta"></i>
                            System Overview
                        </h5>

                        <!-- System Status -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-off-white">Server Status</span>
                                <span class="badge bg-success">Online</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-off-white">Database</span>
                                <span class="badge bg-success">Connected</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-off-white">Storage</span>
                                <span class="badge bg-warning">75% Used</span>
                            </div>
                        </div>

                        <!-- Performance Metrics -->
                        <div class="mb-4">
                            <h6 class="text-cyan mb-3">Performance</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small class="text-off-white">Page Load Speed</small>
                                    <small class="text-success">1.2s</small>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-success" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small class="text-off-white">Memory Usage</small>
                                    <small class="text-warning">68%</small>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-warning" style="width: 68%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity Summary -->
                        <div>
                            <h6 class="text-cyan mb-3">Today's Activity</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-off-white">
                                    <i class="fas fa-shopping-cart me-1"></i>New Orders
                                </span>
                                <span class="text-white fw-semibold">0</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-off-white">
                                    <i class="fas fa-users me-1"></i>New Customers
                                </span>
                                <span class="text-white fw-semibold">0</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-off-white">
                                    <i class="fas fa-eye me-1"></i>Page Views
                                </span>
                                <span class="text-white fw-semibold">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Dashboard JavaScript for Unified Design
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏪 CYPTSHOP Unified Admin Dashboard Loaded');

    // Initialize enhanced dashboard features
    initializeUnifiedDashboard();
    initializeSalesChart();
    initializeRealTimeUpdates();
    initializeLoadingStates();
});

/**
 * Initialize unified dashboard features
 */
function initializeUnifiedDashboard() {
    // Add smooth animations to stat cards
    const statCards = document.querySelectorAll('.dashboard-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in');
    });

    // Initialize tooltips for action buttons
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        new bootstrap.Tooltip(element);
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

/**
 * Initialize sales chart with Chart.js
 */
function initializeSalesChart() {
    const ctx = document.getElementById('salesChart');
    if (!ctx) return;

    window.salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Sales',
                data: [0, 0, 0, 0, 0, 0, 0],
                borderColor: '#00FFFF',
                backgroundColor: 'rgba(0, 255, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: 'Orders',
                data: [0, 0, 0, 0, 0, 0, 0],
                borderColor: '#FF00FF',
                backgroundColor: 'rgba(255, 0, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
}

/**
 * Initialize real-time updates
 */
function initializeRealTimeUpdates() {
    // Update dashboard stats every 30 seconds
    setInterval(updateDashboardStats, 30000);

    // Check for new orders every 10 seconds
    setInterval(checkNewOrders, 10000);

    // Update last updated timestamp
    setInterval(updateTimestamp, 60000);
}

/**
 * Initialize loading states
 */
function initializeLoadingStates() {
    // Add loading states to stat cards
    const loadingOverlays = document.querySelectorAll('.loading-overlay');
    loadingOverlays.forEach(overlay => {
        // Hide loading overlay after initial load
        setTimeout(() => {
            overlay.classList.remove('show');
        }, 1000);
    });
}

/**
 * Enhanced refresh function with loading states
 */
function refreshDashboard() {
    const refreshBtn = document.getElementById('refreshBtn');
    const originalText = refreshBtn.innerHTML;

    // Show loading state
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
    refreshBtn.disabled = true;

    // Show loading overlays
    showLoadingStates();

    // Simulate data refresh
    setTimeout(() => {
        updateDashboardStats();
        checkNewOrders();
        updateTimestamp();

        // Hide loading states
        hideLoadingStates();

        // Reset button
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;

        // Show success notification
        showNotification('Dashboard refreshed successfully!', 'success');
    }, 2000);
}

/**
 * Show loading states for all stat cards
 */
function showLoadingStates() {
    const loadingOverlays = document.querySelectorAll('.loading-overlay');
    loadingOverlays.forEach(overlay => {
        overlay.classList.add('show');
    });
}

/**
 * Hide loading states for all stat cards
 */
function hideLoadingStates() {
    const loadingOverlays = document.querySelectorAll('.loading-overlay');
    loadingOverlays.forEach(overlay => {
        overlay.classList.remove('show');
    });
}

/**
 * Update chart data based on time period
 */
function updateChart(period) {
    if (!window.salesChart) return;

    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Sample data for different periods
    const data = {
        '7days': {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            sales: [120, 190, 300, 500, 200, 300, 450],
            orders: [12, 19, 30, 50, 20, 30, 45]
        },
        '30days': {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            sales: [1200, 1900, 3000, 2500],
            orders: [120, 190, 300, 250]
        },
        '90days': {
            labels: ['Month 1', 'Month 2', 'Month 3'],
            sales: [8000, 12000, 15000],
            orders: [800, 1200, 1500]
        }
    };

    const chartData = data[period] || data['7days'];

    window.salesChart.data.labels = chartData.labels;
    window.salesChart.data.datasets[0].data = chartData.sales;
    window.salesChart.data.datasets[1].data = chartData.orders;
    window.salesChart.update();
}

/**
 * Handle keyboard shortcuts
 */
function handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + R for refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }

    // Alt + N for new product
    if (e.altKey && e.key === 'n') {
        e.preventDefault();
        window.location.href = '<?php echo SITE_URL; ?>/admin/products.php?action=add';
    }
}

/**
 * Update timestamp display
 */
function updateTimestamp() {
    const timestampElement = document.getElementById('lastUpdated');
    if (timestampElement) {
        timestampElement.textContent = new Date().toLocaleString();
    }
}

/**
 * Show notification (fallback if ajax not available)
 */
function showNotification(message, type = 'info') {
    if (typeof ajax !== 'undefined' && ajax.showNotification) {
        ajax.showNotification(message, type);
    } else {
        console.log(`${type.toUpperCase()}: ${message}`);

        // Create simple toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'info'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
}

/**
 * Export dashboard data
 */
function exportDashboardData() {
    showNotification('Preparing dashboard export...', 'info');

    // Simulate export process
    setTimeout(() => {
        showNotification('Dashboard data exported successfully!', 'success');
    }, 2000);
}

/**
 * Print dashboard report
 */
function printDashboard() {
    window.print();
}



/**
 * Update dashboard statistics
 */
async function updateDashboardStats() {
    try {
        const response = await ajax.get('/admin/ajax/dashboard-stats.php');

        if (response.success) {
            const stats = response.data.stats;

            // Update stat cards with animation
            updateStatCard('total-orders', stats.total_orders);
            updateStatCard('total-revenue', '$' + formatNumber(stats.total_revenue, 2));
            updateStatCard('total-customers', stats.total_customers);
            updateStatCard('pending-orders', stats.pending_orders);

            console.log('Dashboard stats updated:', stats);
        }
    } catch (error) {
        console.error('Failed to update dashboard stats:', error);
    }
}

/**
 * Update individual stat card
 */
function updateStatCard(cardId, value) {
    // Find stat cards by their content
    const statCards = document.querySelectorAll('.stat-card-value');
    statCards.forEach(card => {
        const label = card.parentElement.querySelector('.stat-card-label');
        if (label) {
            const labelText = label.textContent.toLowerCase();
            if ((cardId === 'total-orders' && labelText.includes('orders')) ||
                (cardId === 'total-revenue' && labelText.includes('revenue')) ||
                (cardId === 'total-customers' && labelText.includes('customers')) ||
                (cardId === 'pending-orders' && labelText.includes('pending'))) {

                // Add animation effect
                card.style.transform = 'scale(1.1)';
                card.style.transition = 'transform 0.2s ease';
                card.textContent = value;

                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 200);
            }
        }
    });
}

/**
 * Check for new orders and show notifications
 */
async function checkNewOrders() {
    try {
        const response = await ajax.get('/admin/ajax/dashboard-stats.php');

        if (response.success && response.data.recent_orders) {
            // Store previous order count to detect new orders
            if (!window.lastOrderCount) {
                window.lastOrderCount = response.data.stats.total_orders;
                return;
            }

            if (response.data.stats.total_orders > window.lastOrderCount) {
                const newOrdersCount = response.data.stats.total_orders - window.lastOrderCount;
                ajax.showNotification(
                    `${newOrdersCount} new order${newOrdersCount > 1 ? 's' : ''} received!`,
                    'success',
                    8000
                );
                window.lastOrderCount = response.data.stats.total_orders;
            }
        }
    } catch (error) {
        console.error('Failed to check new orders:', error);
    }
}

/**
 * Format number with commas and decimals
 */
function formatNumber(num, decimals = 0) {
    return parseFloat(num).toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}
</script>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>



<?php
/**
 * Admin Dashboard Homepage
 * CYPTSHOP - Task 7.1.2: Dashboard Overview
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Require admin access
requireAdmin();

// No database connection needed for JSON-based system

$pageTitle = 'Admin Dashboard';
$bodyClass = 'admin-dashboard';

// Get dashboard statistics from JSON files
try {
    $users = getJsonData(USERS_JSON) ?: [];
    $orders = getJsonData(ORDERS_JSON) ?: [];
    $products = getJsonData(PRODUCTS_JSON) ?: [];
    $contacts = getJsonData(CONTACTS_JSON) ?: [];

    $totalCustomers = count(array_filter($users, function($user) { return ($user['role'] ?? '') === 'customer'; }));
    $totalOrders = count($orders);
    $totalProducts = count($products);
    $totalRevenue = array_sum(array_column($orders, 'total'));
    $totalContacts = count($contacts);

    // Recent activity
    $recentOrders = array_slice(array_reverse($orders), 0, 5);
    $recentContacts = array_slice(array_reverse($contacts), 0, 5);
} catch (Exception $e) {
    // Fallback values if database is not available
    $totalCustomers = 0;
    $totalOrders = 0;
    $totalProducts = 0;
    $totalRevenue = 0;
    $totalContacts = 0;
    $recentOrders = [];
    $recentContacts = [];
    error_log('Admin dashboard error: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - CYPTSHOP Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">

    <style>
        body {
            background-color: var(--admin-bg, #1a1a1a);
            color: var(--admin-text, #ffffff);
        }
    </style>
</head>
<body class="<?php echo $bodyClass; ?>"><?php
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
            <div>
                <h1 class="h2 text-white">
                    <i class="fas fa-tachometer-alt me-2 text-cyan"></i>
                    Dashboard Overview
                </h1>
                <p class="text-off-white mb-0">Manage your CYPTSHOP store from this central dashboard</p>
            </div>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-cyan" onclick="refreshDashboard()">
                        <i class="fas fa-sync me-1"></i>Refresh
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-cyan">
                        <i class="fas fa-download me-1"></i>Export Data
                    </button>
                </div>
            </div>
        </div>


            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-cyan text-uppercase mb-1">Total Customers</div>
                                    <div class="h5 mb-0 font-weight-bold text-white"><?php echo $totalCustomers; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-cyan"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-magenta text-uppercase mb-1">Total Orders</div>
                                    <div class="h5 mb-0 font-weight-bold text-white"><?php echo $totalOrders; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-magenta"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-yellow text-uppercase mb-1">Total Products</div>
                                    <div class="h5 mb-0 font-weight-bold text-white"><?php echo $totalProducts; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-box fa-2x text-yellow"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-cyan text-uppercase mb-1">Total Revenue</div>
                                    <div class="h5 mb-0 font-weight-bold text-white">$<?php echo number_format($totalRevenue, 2); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-cyan"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Statistics Row -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-magenta text-uppercase mb-1">Active Coupons</div>
                                    <div class="h5 mb-0 font-weight-bold text-white">0</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-ticket-alt fa-2x text-magenta"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-yellow text-uppercase mb-1">Coupon Savings</div>
                                    <div class="h5 mb-0 font-weight-bold text-white">$0.00</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-percentage fa-2x text-yellow"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-cyan text-uppercase mb-1">Portfolio Items</div>
                                    <div class="h5 mb-0 font-weight-bold text-white">0</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-images fa-2x text-cyan"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card bg-dark-grey-1 border-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Categories</div>
                                    <div class="h5 mb-0 font-weight-bold text-white"><?php echo count(getJsonData(CATEGORIES_JSON)); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-tags fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-dark-grey-3">
                        <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                            <h6 class="m-0 font-weight-bold text-cyan">Recent Orders</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-dark table-striped">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Customer</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($orders)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-off-white">No orders yet</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach (array_slice($orders, -5) as $order): ?>
                                                <tr>
                                                    <td class="text-cyan">#<?php echo htmlspecialchars($order['id']); ?></td>
                                                    <td class="text-white"><?php echo htmlspecialchars($order['customer_name'] ?? 'Guest'); ?></td>
                                                    <td class="text-white">$<?php echo number_format($order['total'], 2); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $order['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                                            <?php echo ucfirst($order['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td class="text-off-white"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-dark-grey-3">
                        <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                            <h6 class="m-0 font-weight-bold text-magenta">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?php echo SITE_URL; ?>/admin/products.php?action=add" class="btn btn-cyan">
                                    <i class="fas fa-plus me-2"></i>Add New Product
                                </a>
                                <a href="<?php echo SITE_URL; ?>/admin/coupons.php" class="btn btn-magenta">
                                    <i class="fas fa-ticket-alt me-2"></i>Manage Coupons
                                </a>
                                <a href="<?php echo SITE_URL; ?>/admin/orders.php" class="btn btn-yellow text-black">
                                    <i class="fas fa-shopping-cart me-2"></i>View All Orders
                                </a>
                                <a href="<?php echo SITE_URL; ?>/admin/portfolio.php" class="btn btn-outline-cyan">
                                    <i class="fas fa-images me-2"></i>Manage Portfolio
                                </a>
                                <a href="<?php echo SITE_URL; ?>/admin/hero.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-image me-2"></i>Update Hero Content
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🏪 CYPTSHOP Admin Dashboard Loaded');

    // Initialize real-time dashboard updates
    initializeDashboardUpdates();
});

/**
 * Initialize real-time dashboard updates
 */
function initializeDashboardUpdates() {
    // Update dashboard stats every 30 seconds
    setInterval(updateDashboardStats, 30000);

    // Check for new orders every 10 seconds
    setInterval(checkNewOrders, 10000);
}

/**
 * Manual refresh function for dashboard
 */
function refreshDashboard() {
    updateDashboardStats();
    checkNewOrders();

    // Show notification
    if (typeof ajax !== 'undefined') {
        ajax.showNotification('Dashboard refreshed successfully', 'success');
    } else {
        console.log('✅ Dashboard refreshed');
    }
}



/**
 * Update dashboard statistics
 */
async function updateDashboardStats() {
    try {
        const response = await ajax.get('/admin/ajax/dashboard-stats.php');

        if (response.success) {
            const stats = response.data.stats;

            // Update stat cards with animation
            updateStatCard('total-orders', stats.total_orders);
            updateStatCard('total-revenue', '$' + formatNumber(stats.total_revenue, 2));
            updateStatCard('total-customers', stats.total_customers);
            updateStatCard('pending-orders', stats.pending_orders);

            console.log('Dashboard stats updated:', stats);
        }
    } catch (error) {
        console.error('Failed to update dashboard stats:', error);
    }
}

/**
 * Update individual stat card
 */
function updateStatCard(cardId, value) {
    // Find stat cards by their content
    const statCards = document.querySelectorAll('.stat-card-value');
    statCards.forEach(card => {
        const label = card.parentElement.querySelector('.stat-card-label');
        if (label) {
            const labelText = label.textContent.toLowerCase();
            if ((cardId === 'total-orders' && labelText.includes('orders')) ||
                (cardId === 'total-revenue' && labelText.includes('revenue')) ||
                (cardId === 'total-customers' && labelText.includes('customers')) ||
                (cardId === 'pending-orders' && labelText.includes('pending'))) {

                // Add animation effect
                card.style.transform = 'scale(1.1)';
                card.style.transition = 'transform 0.2s ease';
                card.textContent = value;

                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 200);
            }
        }
    });
}

/**
 * Check for new orders and show notifications
 */
async function checkNewOrders() {
    try {
        const response = await ajax.get('/admin/ajax/dashboard-stats.php');

        if (response.success && response.data.recent_orders) {
            // Store previous order count to detect new orders
            if (!window.lastOrderCount) {
                window.lastOrderCount = response.data.stats.total_orders;
                return;
            }

            if (response.data.stats.total_orders > window.lastOrderCount) {
                const newOrdersCount = response.data.stats.total_orders - window.lastOrderCount;
                ajax.showNotification(
                    `${newOrdersCount} new order${newOrdersCount > 1 ? 's' : ''} received!`,
                    'success',
                    8000
                );
                window.lastOrderCount = response.data.stats.total_orders;
            }
        }
    } catch (error) {
        console.error('Failed to check new orders:', error);
    }
}

/**
 * Format number with commas and decimals
 */
function formatNumber(num, decimals = 0) {
    return parseFloat(num).toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}
</script>

<?php include __DIR__ . '/includes/admin-footer-unified.php'; ?>



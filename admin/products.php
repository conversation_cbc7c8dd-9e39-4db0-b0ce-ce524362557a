<?php
/**
 * Admin Products Management
 * CYPTSHOP - Task 8.1.1: Product Management
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'config.php';
require_once BASE_PATH . 'includes/auth.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'add' || $action === 'edit') {
            // Add/Edit product
            $productData = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'long_description' => trim($_POST['long_description'] ?? ''),
                'price' => floatval($_POST['price'] ?? 0),
                'sale_price' => !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null,
                'category' => $_POST['category'] ?? '',
                'image' => $_POST['image'] ?? 'placeholder.jpg',
                'stock' => intval($_POST['stock'] ?? 0),
                'featured' => isset($_POST['featured']),
                'active' => isset($_POST['active']),
                'sizes' => array_filter(explode(',', $_POST['sizes'] ?? '')),
                'colors' => array_filter(explode(',', $_POST['colors'] ?? '')),
                'features' => array_filter(explode("\n", $_POST['features'] ?? '')),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Debug logging
            error_log("Product save attempt - Action: $action, Name: " . $productData['name'] . ", Price: " . $productData['price']);

            if (empty($productData['name']) || $productData['price'] <= 0) {
                $error = 'Product name and valid price are required.';
                error_log("Product validation failed - Name: " . $productData['name'] . ", Price: " . $productData['price']);
            } else {
                if ($action === 'add') {
                    // Add new product to JSON database
                    try {
                        $products = getJsonData(PRODUCTS_JSON);

                        // Generate unique ID
                        $productData['id'] = generateUniqueId($products);
                        $productData['created_at'] = date('Y-m-d H:i:s');

                        // Add to products array
                        $products[] = $productData;

                        // Save to JSON file
                        if (saveJsonData(PRODUCTS_JSON, $products)) {
                            $success = 'Product added successfully! Product ID: ' . $productData['id'];
                        } else {
                            $error = 'Failed to save product to database.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        error_log("Product creation exception: " . $e->getMessage());
                    }
                } else {
                    // Update existing product
                    $productId = $_POST['product_id'] ?? '';
                    try {
                        $products = getJsonData(PRODUCTS_JSON);
                        $updated = false;

                        foreach ($products as &$product) {
                            if ($product['id'] === $productId) {
                                // Preserve original creation data
                                $productData['id'] = $productId;
                                $productData['created_at'] = $product['created_at'] ?? date('Y-m-d H:i:s');

                                // Update product
                                $product = $productData;
                                $updated = true;
                                break;
                            }
                        }

                        if ($updated && saveJsonData(PRODUCTS_JSON, $products)) {
                            $success = 'Product updated successfully!';
                        } else {
                            $error = 'Failed to update product.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        error_log("Product update exception: " . $e->getMessage());
                    }
                }
            }
        } elseif ($action === 'delete') {
            $productId = $_POST['product_id'] ?? '';
            try {
                $products = getJsonData(PRODUCTS_JSON);
                $originalCount = count($products);

                // Filter out the product to delete
                $products = array_filter($products, function($product) use ($productId) {
                    return $product['id'] !== $productId;
                });

                // Re-index array
                $products = array_values($products);

                if (count($products) < $originalCount && saveJsonData(PRODUCTS_JSON, $products)) {
                    $success = 'Product deleted successfully!';
                } else {
                    $error = 'Failed to delete product.';
                }
            } catch (Exception $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get products and categories from JSON files
try {
    $products = getJsonData(PRODUCTS_JSON);
    $categories = getJsonData(CATEGORIES_JSON);
} catch (Exception $e) {
    $products = [];
    $categories = [];
    $error = 'Database connection error: ' . $e->getMessage();
}

// Get product for editing
$editProduct = null;
if (isset($_GET['edit'])) {
    $editId = $_GET['edit'];
    try {
        foreach ($products as $product) {
            if ($product['id'] === $editId) {
                $editProduct = $product;
                break;
            }
        }
    } catch (Exception $e) {
        $error = 'Failed to load product for editing: ' . $e->getMessage();
    }
}

$pageTitle = 'Product Management - Admin';
$bodyClass = 'admin-products';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced Dark Mode Styling for Admin Products Page */

/* Table Text Readability Improvements */
.table-dark td.text-off-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 500 !important;
}

/* Category Column Enhanced */
.table-dark td:nth-child(4) {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
}

/* Product Category Text */
.table-dark .category-text {
    color: #00FFFF !important;
    font-weight: 600 !important;
    background: rgba(0, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

/* Edit Button Enhanced */
.btn-outline-magenta {
    color: #FF00FF !important;
    border-color: #FF00FF !important;
    background: rgba(255, 0, 255, 0.1) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn-outline-magenta:hover {
    color: #000000 !important;
    background-color: #FF00FF !important;
    border-color: #FF00FF !important;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

.btn-outline-magenta:focus {
    color: #000000 !important;
    background-color: #FF00FF !important;
    border-color: #FF00FF !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 0, 255, 0.25);
}

/* Action Buttons Group Enhanced */
.btn-group-sm .btn {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.875rem !important;
    border-width: 2px !important;
}

.btn-group-sm .btn i {
    font-size: 0.875rem !important;
}

/* View Button Enhanced */
.btn-outline-cyan {
    color: #00FFFF !important;
    border-color: #00FFFF !important;
    background: rgba(0, 255, 255, 0.1) !important;
    font-weight: 600 !important;
}

.btn-outline-cyan:hover {
    color: #000000 !important;
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Delete Button Enhanced */
.btn-outline-danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important;
    font-weight: 600 !important;
}

.btn-outline-danger:hover {
    color: #ffffff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* Table Header Enhanced */
.table-dark thead th {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 700 !important;
    font-size: 0.95rem !important;
    border-bottom: 2px solid rgba(0, 255, 255, 0.3) !important;
    background: rgba(0, 0, 0, 0.3) !important;
}

/* Product Name Enhanced */
.table-dark .text-white {
    color: rgba(255, 255, 255, 0.98) !important;
    font-weight: 600 !important;
}

/* Price Display Enhanced */
.table-dark .text-cyan {
    color: #00FFFF !important;
    font-weight: 700 !important;
}

.table-dark .text-muted {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Status Badges Enhanced */
.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: #000000 !important;
    font-weight: 600 !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    color: #000000 !important;
    font-weight: 600 !important;
}

.badge.bg-cyan {
    background: linear-gradient(135deg, #00FFFF, #00e6e6) !important;
    color: #000000 !important;
    font-weight: 600 !important;
}

/* Table Row Hover Effect */
.table-dark tbody tr:hover {
    background-color: rgba(0, 255, 255, 0.05) !important;
}

.table-dark tbody tr:hover td {
    color: rgba(255, 255, 255, 1) !important;
}

/* Form Elements in Modal Enhanced */
.modal-content {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
}

.modal-header {
    background: rgba(0, 0, 0, 0.3) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
}

.modal-title {
    color: #00FFFF !important;
    font-weight: 700 !important;
}

.form-label.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
}

.form-control.text-white,
.form-select.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    background-color: #2d2d2d !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.form-control:focus,
.form-select:focus {
    border-color: #00FFFF !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

/* Image Gallery Enhanced */
.image-item {
    background: rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
}

.image-item:hover {
    border-color: rgba(0, 255, 255, 0.5) !important;
    background: rgba(0, 255, 255, 0.05) !important;
}

/* Button Group in Image Gallery */
.btn-group .btn-sm {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
}

.btn-outline-warning {
    color: #ffc107 !important;
    border-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.1) !important;
}

.btn-outline-warning:hover {
    color: #000000 !important;
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
}

.btn-outline-info {
    color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    background: rgba(23, 162, 184, 0.1) !important;
}

.btn-outline-info:hover {
    color: #ffffff !important;
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .table-dark td {
        font-size: 0.875rem !important;
        padding: 0.5rem 0.25rem !important;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.375rem !important;
        font-size: 0.75rem !important;
    }

    .btn-group-sm .btn i {
        font-size: 0.75rem !important;
    }
}

/* Loading States */
.table-dark tbody tr.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Success/Error States */
.table-dark tbody tr.success {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.table-dark tbody tr.error {
    background-color: rgba(220, 53, 69, 0.1) !important;
}
</style>

<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Product Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#productModal">
                        <i class="fas fa-plus me-2"></i>Add Product
                    </button>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Products Table -->
            <div class="card bg-dark-grey-1 border-dark-grey-3">
                <div class="card-header bg-dark-grey-2 border-dark-grey-3">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-box me-2"></i>
                        Products (<?php echo count($products); ?>)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($products)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <img src="<?php echo SITE_URL; ?>/assets/images/products/<?php echo $product['image'] ?? 'placeholder.jpg'; ?>" 
                                                     class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;" 
                                                     alt="<?php echo htmlspecialchars($product['name'] ?? ''); ?>">
                                            </td>
                                            <td>
                                                <strong class="text-white"><?php echo htmlspecialchars($product['name'] ?? ''); ?></strong>
                                                <?php if (isset($product['featured']) && $product['featured']): ?>
                                                    <span class="badge bg-cyan text-black ms-2">Featured</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-off-white"><?php echo htmlspecialchars(ucfirst($product['category'] ?? 'Uncategorized')); ?></td>
                                            <td>
                                                <?php if (isset($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                                                    <span class="text-cyan">$<?php echo number_format($product['sale_price'], 2); ?></span>
                                                    <small class="text-muted text-decoration-line-through">$<?php echo number_format($product['price'], 2); ?></small>
                                                <?php else: ?>
                                                    <span class="text-cyan">$<?php echo number_format($product['price'], 2); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="<?php echo ($product['stock'] ?? 0) > 10 ? 'text-success' : (($product['stock'] ?? 0) > 0 ? 'text-warning' : 'text-danger'); ?>">
                                                    <?php echo $product['stock'] ?? 0; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (isset($product['active']) && $product['active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                                       class="btn btn-outline-cyan" target="_blank" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-outline-magenta edit-product" 
                                                            data-product='<?php echo htmlspecialchars(json_encode($product)); ?>' title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger delete-product" 
                                                            data-product-id="<?php echo $product['id']; ?>" 
                                                            data-product-name="<?php echo htmlspecialchars($product['name'] ?? ''); ?>" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-box fa-3x text-dark-grey-3 mb-3"></i>
                            <h5 class="text-white">No products found</h5>
                            <p class="text-off-white">Start by adding your first product.</p>
                            <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#productModal">
                                <i class="fas fa-plus me-2"></i>Add Product
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- Product Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="productModalTitle">
                    <i class="fas fa-plus me-2"></i>Add Product
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="productForm">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="name" class="form-label text-white fw-bold">Product Name *</label>
                            <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="name" name="name" required>
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label text-white fw-bold">Category</label>
                            <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="category" name="category">
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category['slug'] ?? ''); ?>">
                                        <?php echo htmlspecialchars($category['name'] ?? ''); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label text-white fw-bold">Short Description</label>
                            <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="description" name="description" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="long_description" class="form-label text-white fw-bold">Long Description</label>
                            <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="long_description" name="long_description" rows="4"></textarea>
                        </div>
                        <div class="col-md-4">
                            <label for="price" class="form-label text-white fw-bold">Price *</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark-grey-2 border-dark-grey-3 text-white">$</span>
                                <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="price" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="sale_price" class="form-label text-white fw-bold">Sale Price</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark-grey-2 border-dark-grey-3 text-white">$</span>
                                <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="sale_price" name="sale_price" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="stock" class="form-label text-white fw-bold">Stock Quantity</label>
                            <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="stock" name="stock" min="0" value="0">
                        </div>
                        <div class="col-md-6">
                            <label for="sizes" class="form-label text-white fw-bold">Sizes (comma separated)</label>
                            <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="sizes" name="sizes" placeholder="S, M, L, XL, XXL">
                        </div>
                        <div class="col-md-6">
                            <label for="colors" class="form-label text-white fw-bold">Colors (comma separated)</label>
                            <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="colors" name="colors" placeholder="Black, White, Grey">
                        </div>
                        <div class="col-12">
                            <label for="features" class="form-label text-white fw-bold">Features (one per line)</label>
                            <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" id="features" name="features" rows="3" placeholder="100% Cotton&#10;Machine Washable&#10;Pre-shrunk"></textarea>
                        </div>
                        <!-- Product Images Section -->
                        <div class="col-12">
                            <hr class="border-dark-grey-3 my-4">
                            <h6 class="text-cyan mb-3">
                                <i class="fas fa-images me-2"></i>Product Images
                            </h6>
                        </div>

                        <!-- Image Upload Area -->
                        <div class="col-12" id="imageUploadSection" style="display: none;">
                            <div class="card bg-dark-grey-2 border-cyan">
                                <div class="card-header bg-dark-grey-3 border-cyan">
                                    <h6 class="mb-0 text-cyan">Upload Images</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <!-- Drag & Drop Upload Area -->
                                            <div class="upload-area border border-dashed border-cyan rounded p-4 text-center mb-3"
                                                 id="productUploadArea" ondrop="dropHandler(event);" ondragover="dragOverHandler(event);">
                                                <i class="fas fa-cloud-upload-alt fa-2x text-cyan mb-2"></i>
                                                <h6 class="text-white">Drag & Drop Images Here</h6>
                                                <p class="text-off-white mb-2">or click to browse files</p>
                                                <input type="file" id="productFileInput" multiple accept="image/*" style="display: none;" onchange="handleFiles(this.files)">
                                                <button type="button" class="btn btn-cyan btn-sm" onclick="document.getElementById('productFileInput').click()">
                                                    <i class="fas fa-folder-open me-1"></i>Browse Files
                                                </button>
                                            </div>

                                            <!-- Upload Progress -->
                                            <div id="productUploadProgress" style="display: none;">
                                                <div class="progress mb-2">
                                                    <div class="progress-bar bg-cyan" role="progressbar" style="width: 0%" id="productProgressBar"></div>
                                                </div>
                                                <div class="text-center">
                                                    <span id="productUploadStatus" class="text-white">Uploading...</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <!-- Upload Settings -->
                                            <div class="card bg-dark-grey-3 border-0">
                                                <div class="card-header bg-dark-grey-2">
                                                    <h6 class="mb-0 text-white">Upload Settings</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Image Quality</label>
                                                        <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="productImageQuality">
                                                            <option value="high">High Quality</option>
                                                            <option value="medium" selected>Medium Quality</option>
                                                            <option value="low">Low Quality</option>
                                                        </select>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label text-white">Max Width</label>
                                                        <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="productResizeOption">
                                                            <option value="1200">1200px</option>
                                                            <option value="800" selected>800px</option>
                                                            <option value="600">600px</option>
                                                        </select>
                                                    </div>

                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="productGenerateThumbnails" checked>
                                                        <label class="form-check-label text-white" for="productGenerateThumbnails">
                                                            Generate Thumbnails
                                                        </label>
                                                    </div>

                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="productSetAsFeatured">
                                                        <label class="form-check-label text-white" for="productSetAsFeatured">
                                                            Set as Featured Image
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Images Display -->
                        <div class="col-12">
                            <div id="productImagesContainer">
                                <div class="text-center py-3">
                                    <p class="text-off-white mb-2">No images uploaded yet</p>
                                    <button type="button" class="btn btn-outline-cyan btn-sm" onclick="showImageUpload()">
                                        <i class="fas fa-plus me-1"></i>Add Images
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="featured" name="featured">
                                <label class="form-check-label text-white" for="featured">Featured Product</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="active" name="active" checked>
                                <label class="form-check-label text-white" for="active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Product
                    </button>
                </div>
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="product_id" id="productId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            </form>
        </div>
    </div>
</div>

<script>
// Product image management variables
let selectedFiles = [];
let currentProductId = null;

// Edit product
document.querySelectorAll('.edit-product').forEach(btn => {
    btn.addEventListener('click', function() {
        const product = JSON.parse(this.dataset.product);
        currentProductId = product.id;

        // Fill form with product data
        document.getElementById('name').value = product.name || '';
        document.getElementById('category').value = product.category || '';
        document.getElementById('description').value = product.description || '';
        document.getElementById('long_description').value = product.long_description || '';
        document.getElementById('price').value = product.price || '';
        document.getElementById('sale_price').value = product.sale_price || '';
        document.getElementById('stock').value = product.stock || 0;
        document.getElementById('sizes').value = (product.sizes || []).join(', ');
        document.getElementById('colors').value = (product.colors || []).join(', ');
        document.getElementById('features').value = (product.features || []).join('\n');
        document.getElementById('featured').checked = product.featured || false;
        document.getElementById('active').checked = product.active !== false;

        // Update form action
        document.getElementById('formAction').value = 'edit';
        document.getElementById('productId').value = product.id;
        document.getElementById('productModalTitle').innerHTML = '<i class="fas fa-edit me-2"></i>Edit Product';

        // Load product images
        loadProductImages(product.id);

        // Show modal
        new bootstrap.Modal(document.getElementById('productModal')).show();
    });
});

// Delete product
document.querySelectorAll('.delete-product').forEach(btn => {
    btn.addEventListener('click', function() {
        const productId = this.dataset.productId;
        const productName = this.dataset.productName;
        
        if (confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="product_id" value="${productId}">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    });
});

// Reset form when modal is hidden
document.getElementById('productModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('productForm').reset();
    document.getElementById('formAction').value = 'add';
    document.getElementById('productId').value = '';
    document.getElementById('productModalTitle').innerHTML = '<i class="fas fa-plus me-2"></i>Add Product';
    currentProductId = null;
    hideImageUpload();
    resetImageContainer();
});

// Image management functions
function showImageUpload() {
    document.getElementById('imageUploadSection').style.display = 'block';
}

function hideImageUpload() {
    document.getElementById('imageUploadSection').style.display = 'none';
    selectedFiles = [];
}

function resetImageContainer() {
    document.getElementById('productImagesContainer').innerHTML = `
        <div class="text-center py-3">
            <p class="text-off-white mb-2">No images uploaded yet</p>
            <button type="button" class="btn btn-outline-cyan btn-sm" onclick="showImageUpload()">
                <i class="fas fa-plus me-1"></i>Add Images
            </button>
        </div>
    `;
}

// Drag and drop handlers
function dragOverHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.add('border-magenta');
}

function dropHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.remove('border-magenta');

    const files = ev.dataTransfer.files;
    handleFiles(files);
}

function handleFiles(files) {
    selectedFiles = Array.from(files).filter(file => file.type.startsWith('image/'));

    if (selectedFiles.length === 0) {
        alert('Please select valid image files.');
        return;
    }

    if (!currentProductId) {
        alert('Please save the product first before uploading images.');
        return;
    }

    uploadImages();
}

async function uploadImages() {
    if (selectedFiles.length === 0) return;

    document.getElementById('productUploadProgress').style.display = 'block';

    const totalFiles = selectedFiles.length;
    let uploadedCount = 0;

    for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const formData = new FormData();

        formData.append('file', file);
        formData.append('product_id', currentProductId);
        formData.append('quality', document.getElementById('productImageQuality').value);
        formData.append('resize', document.getElementById('productResizeOption').value);
        formData.append('generate_thumbnails', document.getElementById('productGenerateThumbnails').checked);
        formData.append('set_as_featured', document.getElementById('productSetAsFeatured').checked && i === 0);
        formData.append('title', file.name.split('.')[0]);
        formData.append('alt_text', document.getElementById('name').value || 'Product image');
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        try {
            const response = await fetch('/admin/ajax/upload-product-image.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                uploadedCount++;
                const progress = (uploadedCount / totalFiles) * 100;
                document.getElementById('productProgressBar').style.width = progress + '%';
                document.getElementById('productUploadStatus').textContent = `Uploaded ${uploadedCount} of ${totalFiles} files`;
            } else {
                console.error('Upload failed:', result.message);
                alert('Upload failed: ' + result.message);
            }
        } catch (error) {
            console.error('Upload error:', error);
            alert('Upload error: ' + error.message);
        }
    }

    // Upload complete
    document.getElementById('productUploadStatus').textContent = `Upload complete! ${uploadedCount} files uploaded.`;

    setTimeout(() => {
        document.getElementById('productUploadProgress').style.display = 'none';
        hideImageUpload();
        loadProductImages(currentProductId);
    }, 2000);
}

// Load and display product images
async function loadProductImages(productId) {
    if (!productId) return;

    try {
        const formData = new FormData();
        formData.append('action', 'get_product_images');
        formData.append('product_id', productId);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        const response = await fetch('/admin/ajax/manage-product-images.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            displayProductImages(result.images);
        } else {
            console.error('Failed to load images:', result.message);
        }
    } catch (error) {
        console.error('Error loading images:', error);
    }
}

function displayProductImages(images) {
    const container = document.getElementById('productImagesContainer');

    if (images.length === 0) {
        resetImageContainer();
        return;
    }

    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-white mb-0">Product Images (${images.length})</h6>
            <button type="button" class="btn btn-outline-cyan btn-sm" onclick="showImageUpload()">
                <i class="fas fa-plus me-1"></i>Add More
            </button>
        </div>
        <div class="row g-3" id="imageGrid">
    `;

    images.forEach(image => {
        html += `
            <div class="col-md-3" data-image-id="${image.id}">
                <div class="card bg-dark-grey-2 border-0 position-relative">
                    ${image.is_featured ? '<div class="badge bg-magenta position-absolute top-0 start-0 m-2">Featured</div>' : ''}
                    <img src="${image.urls.medium}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="${image.alt_text}">
                    <div class="card-body p-2">
                        <h6 class="card-title text-white mb-1" style="font-size: 0.8rem;">${image.title}</h6>
                        <small class="text-off-white">${image.file_size_formatted}</small>
                        <div class="btn-group w-100 mt-2">
                            ${!image.is_featured ? `<button class="btn btn-sm btn-outline-warning" onclick="setFeaturedImage(${image.id})" title="Set as Featured">
                                <i class="fas fa-star"></i>
                            </button>` : ''}
                            <button class="btn btn-sm btn-outline-info" onclick="editImageDetails(${image.id}, '${image.title}', '${image.alt_text}')" title="Edit Details">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProductImage(${image.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Set featured image
async function setFeaturedImage(imageId) {
    try {
        const formData = new FormData();
        formData.append('action', 'set_featured');
        formData.append('image_id', imageId);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        const response = await fetch('/admin/ajax/manage-product-images.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            loadProductImages(currentProductId);
        } else {
            alert('Failed to set featured image: ' + result.message);
        }
    } catch (error) {
        console.error('Error setting featured image:', error);
        alert('Error setting featured image');
    }
}

// Delete product image
async function deleteProductImage(imageId) {
    if (!confirm('Are you sure you want to delete this image?')) return;

    try {
        const formData = new FormData();
        formData.append('action', 'delete_image');
        formData.append('image_id', imageId);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        const response = await fetch('/admin/ajax/manage-product-images.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            loadProductImages(currentProductId);
        } else {
            alert('Failed to delete image: ' + result.message);
        }
    } catch (error) {
        console.error('Error deleting image:', error);
        alert('Error deleting image');
    }
}

// Edit image details
function editImageDetails(imageId, currentTitle, currentAltText) {
    const newTitle = prompt('Enter image title:', currentTitle);
    if (newTitle === null) return;

    const newAltText = prompt('Enter alt text:', currentAltText);
    if (newAltText === null) return;

    updateImageDetails(imageId, newTitle, newAltText);
}

async function updateImageDetails(imageId, title, altText) {
    try {
        const formData = new FormData();
        formData.append('action', 'update_image_details');
        formData.append('image_id', imageId);
        formData.append('title', title);
        formData.append('alt_text', altText);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

        const response = await fetch('/admin/ajax/manage-product-images.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            loadProductImages(currentProductId);
        } else {
            alert('Failed to update image details: ' + result.message);
        }
    } catch (error) {
        console.error('Error updating image details:', error);
        alert('Error updating image details');
    }
}

// Initialize drag and drop
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('productUploadArea');
    if (uploadArea) {
        uploadArea.addEventListener('dragenter', function(e) {
            e.preventDefault();
            this.classList.add('border-magenta');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            if (!this.contains(e.relatedTarget)) {
                this.classList.remove('border-magenta');
            }
        });
    }
});
</script>



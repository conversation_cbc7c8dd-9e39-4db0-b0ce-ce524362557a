<?php
// Prevent direct access to this file
if (!defined('BASE_PATH')) {
    define('BASE_PATH', __DIR__ . '/');
}

// Site Configuration
define('SITE_NAME', 'CYPTSHOP');
define('SITE_URL', 'http://localhost:8000'); // Update for production
define('SITE_EMAIL', '<EMAIL>');

// Directory Paths
define('ASSETS_PATH', BASE_PATH . 'assets/');
define('IMAGES_PATH', ASSETS_PATH . 'images/');
define('HERO_PATH', IMAGES_PATH . 'hero/');
define('PRODUCTS_IMAGES_PATH', IMAGES_PATH . 'products/');
define('PORTFOLIO_IMAGES_PATH', IMAGES_PATH . 'portfolio/');
define('SERVICES_IMAGES_PATH', IMAGES_PATH . 'services/');
define('UPLOADS_PATH', ASSETS_PATH . 'uploads/customer/');
define('DATA_PATH', ASSETS_PATH . 'data/');

// JSON Data Files
define('PRODUCTS_JSON', DATA_PATH . 'products.json');
define('CATEGORIES_JSON', DATA_PATH . 'categories.json');
define('ORDERS_JSON', DATA_PATH . 'orders.json');
define('USERS_JSON', DATA_PATH . 'users.json');
define('CUSTOMER_UPLOADS_JSON', DATA_PATH . 'customer_uploads.json');
define('SERVICES_JSON', DATA_PATH . 'services.json');
define('HERO_JSON', DATA_PATH . 'hero.json');

// File Upload Settings
define('ALLOWED_UPLOAD_TYPES', ['image/jpeg', 'image/png', 'application/pdf', 'application/postscript', 'image/svg+xml']); // .jpg, .png, .pdf, .ai, .eps, .svg
define('MAX_UPLOAD_SIZE', 10 * 1024 * 1024); // 10MB in bytes

// PayPal Configuration
define('PAYPAL_CLIENT_ID', 'YOUR_PAYPAL_CLIENT_ID'); // Replace with your PayPal sandbox or live client ID
define('PAYPAL_CURRENCY', 'USD');
define('PAYPAL_SANDBOX', true); // Set to false for production

// Admin Default Credentials (for initial setup, hash passwords in production)
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD_HASH', password_hash('admin123', PASSWORD_DEFAULT)); // Change password and hash in production

// Session Settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/Detroit'); // Detroit timezone for consistency

// Include simple JSON database functions
require_once __DIR__ . '/includes/db.php';

// Initialize JSON files if they don't exist
$jsonFiles = [PRODUCTS_JSON, CATEGORIES_JSON, ORDERS_JSON, USERS_JSON, CUSTOMER_UPLOADS_JSON, SERVICES_JSON, HERO_JSON];
foreach ($jsonFiles as $file) {
    if (!file_exists($file)) {
        file_put_contents($file, json_encode([]));
    }
}

// Initialize default services data if empty
if (empty(getJsonData(SERVICES_JSON))) {
    $defaultServices = [
        'hero' => [
            'type' => 'image',
            'src' => 'assets/images/hero/hero-bg.jpg',
            'alt_src' => 'assets/images/hero/hero-bg.jpg',
            'title' => 'CYPTSHOP: Detroit-Style Custom Designs',
            'subtitle' => 'Bold T-Shirts, Prints, and More',
            'cta_text' => 'Shop Now',
            'cta_link' => 'shop.php'
        ],
        'subheroes' => [
            ['page' => 'shop', 'type' => 'image', 'src' => 'assets/images/hero/subhero-shop.jpg', 'title' => 'Explore Our Products', 'subtitle' => 'T-Shirts, Mugs, and More'],
            ['page' => 'services', 'type' => 'image', 'src' => 'assets/images/hero/subhero-services.jpg', 'title' => 'Our Services', 'subtitle' => 'Custom Design and Marketing Solutions'],
            ['page' => 'portfolio', 'type' => 'image', 'src' => 'assets/images/hero/subhero-portfolio.jpg', 'title' => 'Our Work', 'subtitle' => 'See Our Custom Designs'],
            ['page' => 'contact', 'type' => 'image', 'src' => 'assets/images/hero/subhero-contact.jpg', 'title' => 'Get in Touch', 'subtitle' => 'Let’s Create Something Amazing'],
            ['page' => 'cart', 'type' => 'image', 'src' => 'assets/images/hero/subhero-cart.jpg', 'title' => 'Your Cart', 'subtitle' => 'Ready to Checkout?'],
            ['page' => 'checkout', 'type' => 'image', 'src' => 'assets/images/hero/subhero-checkout.jpg', 'title' => 'Checkout', 'subtitle' => 'Complete Your Order'],
            ['page' => 'account', 'type' => 'image', 'src' => 'assets/images/hero/subhero-account.jpg', 'title' => 'Your Account', 'subtitle' => 'Manage Your Orders']
        ],
        'services' => [
            [
                'id' => 1,
                'category' => 'Apparel Design',
                'title' => 'Custom T-Shirts',
                'description' => 'Create unique T-shirts with bold, Detroit-inspired designs.',
                'icon' => 'fas fa-tshirt',
                'image' => 'assets/images/services/tshirt-mockup.jpg'
            ],
            [
                'id' => 2,
                'category' => 'Graphic Design',
                'title' => 'Print Design',
                'description' => 'High-quality business cards, flyers, and booklets.',
                'icon' => 'fas fa-print',
                'image' => 'assets/images/services/print-flyer.jpg'
            ],
            [
                'id' => 3,
                'category' => 'Graphic Design',
                'title' => 'Large Banners',
                'description' => 'Eye-catching vinyl banners for events or storefronts.',
                'icon' => 'fas fa-sign',
                'image' => 'assets/images/services/banner-example.jpg'
            ],
            [
                'id' => 4,
                'category' => 'Digital Services',
                'title' => 'Web Design',
                'description' => 'Modern, responsive websites with urban aesthetics.',
                'icon' => 'fas fa-laptop-code',
                'image' => 'assets/images/services/web-design.jpg'
            ],
            [
                'id' => 5,
                'category' => 'Digital Services',
                'title' => 'Marketing & SEO',
                'description' => 'Boost your brand with social media and SEO strategies.',
                'icon' => 'fas fa-bullhorn',
                'image' => 'assets/images/services/marketing-seo.jpg'
            ]
        ]
    ];
    saveJsonData(SERVICES_JSON, $defaultServices);
}

// Initialize default hero data if empty
if (empty(getJsonData(HERO_JSON))) {
    $defaultHero = [
        'main_title' => 'CYPTSHOP',
        'subtitle' => 'Detroit-Style Custom Design',
        'description' => 'Bold custom T-shirts, apparel design, print services, and digital marketing solutions with authentic Detroit urban aesthetic.',
        'cta_text' => 'Shop Now',
        'cta_link' => '/shop.php',
        'background_image' => 'hero-bg.jpg',
        'active' => true
    ];
    saveJsonData(HERO_JSON, $defaultHero);
}

// Initialize default admin user if none exist
if (empty(getJsonData(USERS_JSON))) {
    $defaultUsers = [
        [
            'id' => '1',
            'role' => 'admin',
            'username' => ADMIN_USERNAME,
            'email' => '<EMAIL>',
            'password' => ADMIN_PASSWORD_HASH,
            'name' => 'Admin User',
            'created_at' => date('Y-m-d')
        ]
    ];
    saveJsonData(USERS_JSON, $defaultUsers);
}
?>